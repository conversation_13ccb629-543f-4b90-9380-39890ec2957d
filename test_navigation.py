#!/usr/bin/env python3

def get_navigation_directions(destination: str, origin: str = None, travel_mode: str = "driving"):
    """Provides navigation directions using Google Maps URLs."""
    print(f"🗺️ Tool Call: get_navigation_directions(destination={destination}, origin={origin}, travel_mode={travel_mode})")
    try:
        formatted_destination = destination.replace(" ", "+").replace(",", "%2C")
        base_url = "https://www.google.com/maps/dir/?api=1"
        maps_url = f"{base_url}&destination={formatted_destination}"
        
        if origin:
            formatted_origin = origin.replace(" ", "+").replace(",", "%2C")
            maps_url += f"&origin={formatted_origin}"
        else:
            maps_url += "&origin=My+Location"
        
        if travel_mode in ["driving", "walking", "bicycling", "transit"]:
            maps_url += f"&travelmode={travel_mode}"
        
        result = {
            "destination": destination,
            "origin": origin if origin else "Current Location",
            "travel_mode": travel_mode,
            "maps_url": maps_url,
            "status": "success",
            "message": f"Navigation ready for {destination}",
        }
        print(f"🗺️ Tool Response: {result}")
        return result
    except Exception as e:
        error_result = {
            "error": f"Error generating navigation: {str(e)}",
            "status": "error",
        }
        print(f"🗺️ Tool Error: {error_result}")
        return error_result

def test_navigation():
    """Test the navigation function"""
    print("Testing navigation function...")
    
    # Test case 1: Simple destination
    result1 = get_navigation_directions("Delhi")
    print(f"Test 1 - Simple destination: {result1}")
    
    # Test case 2: Destination with origin
    result2 = get_navigation_directions("Kathmandu, Nepal", "Guna, Madhya Pradesh")
    print(f"Test 2 - With origin: {result2}")
    
    # Test case 3: Different travel mode
    result3 = get_navigation_directions("Mumbai", travel_mode="walking")
    print(f"Test 3 - Walking mode: {result3}")
    
    # Simulate the response creation
    navigation_urls = []
    
    # Extract URLs from results
    for result in [result1, result2, result3]:
        if isinstance(result, dict) and "maps_url" in result:
            navigation_urls.append(result["maps_url"])
    
    print(f"\nExtracted URLs: {navigation_urls}")
    
    # Create response like the main code
    response_data = {"response": "Navigation ready"}
    
    if navigation_urls:
        response_data["Maps"] = navigation_urls[0]
        print(f"✅ Final response with Maps: {response_data}")
    else:
        print(f"❌ No Maps URL found: {response_data}")

if __name__ == "__main__":
    test_navigation()
