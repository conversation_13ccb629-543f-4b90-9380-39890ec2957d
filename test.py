import os
import requests
from google import genai
from google.genai import types
import wikipedia
from flask import Flask, request, jsonify
import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
import re
import redis
import json
import mysql.connector
from typing import Optional
from decimal import Decimal
import threading

# Initialize Redis client
r = redis.Redis(host="mercury.nityasha.com", port=26739, db=1, password="Amber@!23")

# Function to create hotel_images table if it doesn't exist
def create_hotel_images_table():
    """Create hotel_images table if it doesn't exist"""
    try:
        conn = mysql.connector.connect(
            host="mercury.nityasha.com",
            user="kzzuezbs_31aa9913123139jmasr",
            password="N4(I9_P9>!lPo:vmT0",
            database="kzzuezbs_31aa9913123139jmasr",
        )
        cursor = conn.cursor()

        # Create table if it doesn't exist
        create_table_query = """
        CREATE TABLE IF NOT EXISTS hotel_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hotel_id INT NOT NULL,
            image_url VARCHAR(500) NOT NULL,
            image_description VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_hotel_id (hotel_id)
        )
        """
        cursor.execute(create_table_query)
        conn.commit()
        cursor.close()
        conn.close()
        print("✅ Hotel images table created/verified successfully")
    except mysql.connector.Error as err:
        print(f"❌ Error creating hotel_images table: {err}")

# Create the table on startup
create_hotel_images_table()

# Custom serialization and deserialization functions for google.genai.types.Content
def serialize_content(content: types.Content) -> dict:
    """Serializes a types.Content object into a dictionary for JSON storage."""
    serialized_parts = []
    for part in content.parts:
        if part.text:
            serialized_parts.append({"text": part.text})
        elif part.function_call:
            serialized_parts.append(
                {
                    "function_call": {
                        "name": part.function_call.name,
                        "args": dict(part.function_call.args),
                    }
                }
            )
        elif part.function_response:
            serialized_parts.append(
                {
                    "function_response": {
                        "name": part.function_response.name,
                        "response": part.function_response.response,
                    }
                }
            )
    return {"role": content.role, "parts": serialized_parts}

def filter_response_text(response_text: str) -> str:
    """Filters out ALL unwanted content including Maps URLs, links, and special characters. Also enforces word limit."""
    # Remove ALL types of URLs
    url_patterns = [
        r"https?://[^\s]*",
        r"www\.[^\s]*",
        r"[^\s]*\.com[^\s]*",
        r"[^\s]*\.org[^\s]*",
        r"[^\s]*\.net[^\s]*",
    ]
    for pattern in url_patterns:
        response_text = re.sub(pattern, "", response_text)

    # Remove link-related phrases
    link_phrases = [
        r"यह रहा लिंक:?\s*",
        r"यहाँ है लिंक:?\s*",
        r"गूगल मैप्स का लिंक:?\s*",
        r"Google Maps ka link:?\s*",
        r"yeh raha link:?\s*",
        r"yahaan hai link:?\s*",
        r"link:?\s*",
        r"URL:?\s*",
        r"web address:?\s*",
        r"website:?\s*",
        r"पर जा सकते हैं:?\s*",
        r"par ja sakte hain:?\s*",
        r"dekh sakte hain:?\s*",
        r"देख सकते हैं:?\s*",
    ]
    for phrase in link_phrases:
        response_text = re.sub(phrase, "", response_text, flags=re.IGNORECASE)

    # Remove unwanted special characters
    unwanted_pattern = r"[*!&#)(_{}\[\]<>|~`^@$%+=\\\"']"
    cleaned_text = re.sub(unwanted_pattern, "", response_text)

    # Clean up multiple spaces
    cleaned_text = re.sub(r"\s+", " ", cleaned_text)

    # Remove trailing punctuation after cleanup
    cleaned_text = re.sub(r"[\.:,;]+\s*$", "", cleaned_text)



    return cleaned_text.strip()

def deserialize_content(data: dict) -> types.Content:
    """Deserializes a dictionary back into a types.Content object."""
    deserialized_parts = []
    for part_data in data["parts"]:
        if "text" in part_data:
            deserialized_parts.append(types.Part(text=part_data["text"]))
        elif "function_call" in part_data:
            fc_data = part_data["function_call"]
            deserialized_parts.append(
                types.Part(
                    function_call=types.FunctionCall(
                        name=fc_data["name"], args=fc_data["args"]
                    )
                )
            )
        elif "function_response" in part_data:
            fr_data = part_data["function_response"]
            deserialized_parts.append(
                types.Part(
                    function_response=types.FunctionResponse(
                        name=fr_data["name"], response=fr_data["response"]
                    )
                )
            )
    return types.Content(parts=deserialized_parts, role=data["role"])

# Tool functions
def get_weather_forecast(location: str) -> dict:
    """Gets the current weather temperature for a given location."""
    print(f"Tool Call: get_weather_forecast(location={location})")
    return {"temperature": 25, "unit": "celsius"}

def set_thermostat_temperature(temperature: int) -> dict:
    """Sets the thermostat to a desired temperature."""
    print(f"Tool Call: set_thermostat_temperature(temperature={temperature})")
    return {"status": "success"}

def search_hotels_from_db(
    search_term: Optional[str] = None,
    city: Optional[str] = None,
    tags: Optional[str] = None,
) -> dict:
    """Search the hotel database across ALL columns using full-text search or comprehensive LIKE search.
    Use this for: hotels, restaurants, accommodation, lodging, stay, rooms, booking, hospitality services."""
    print(f"Tool Call: search_hotels_from_db(search_term={search_term}, city={city}, tags={tags})")
    try:
        conn = mysql.connector.connect(
            host="mercury.nityasha.com",
            user="kzzuezbs_31aa9913123139jmasr",
            password="N4(I9_P9>!lPo:vmT0",
            database="kzzuezbs_31aa9913123139jmasr",
        )
        cursor = conn.cursor(dictionary=True)
        params = []

        if search_term and not city and not tags:
            try:
                query = """
                    SELECT h.*, GROUP_CONCAT(hi.image_url) as images
                    FROM info h
                    LEFT JOIN hotel_images hi ON h.id = hi.hotel_id
                    WHERE MATCH(h.name, h.address, h.about, h.pricing, h.offers, h.owner_name, h.city, h.tags)
                     AGAINST (%s IN NATURAL LANGUAGE MODE)
                    GROUP BY h.id
                    ORDER BY MATCH(h.name, h.address, h.about, h.pricing, h.offers, h.owner_name, h.city, h.tags)
                     AGAINST (%s IN NATURAL LANGUAGE MODE) DESC
                    LIMIT 10
                """
                params = [search_term, search_term]
                cursor.execute(query, params)
                results = cursor.fetchall()
            except mysql.connector.Error:
                query = """
                    SELECT h.*, GROUP_CONCAT(hi.image_url) as images
                    FROM info h
                    LEFT JOIN hotel_images hi ON h.id = hi.hotel_id
                    WHERE (
                        CAST(h.id AS CHAR) LIKE %s OR
                        h.name LIKE %s OR
                        h.address LIKE %s OR
                        h.number LIKE %s OR
                        h.about LIKE %s OR
                        h.pricing LIKE %s OR
                        h.offers LIKE %s OR
                        h.owner_name LIKE %s OR
                        CAST(h.rating AS CHAR) LIKE %s OR
                        h.reviews LIKE %s OR
                        h.city LIKE %s OR
                        h.tags LIKE %s
                    )
                    GROUP BY h.id
                    ORDER BY h.rating DESC
                    LIMIT 10
                """
                search_pattern = f"%{search_term}%"
                params = [search_pattern] * 12
                cursor.execute(query, params)
                results = cursor.fetchall()
        else:
            query = """
                SELECT h.*, GROUP_CONCAT(hi.image_url) as images
                FROM info h
                LEFT JOIN hotel_images hi ON h.id = hi.hotel_id
                WHERE 1=1
            """
            if search_term:
                query += """ AND (
                    CAST(h.id AS CHAR) LIKE %s OR
                    h.name LIKE %s OR
                    h.address LIKE %s OR
                    h.number LIKE %s OR
                    h.about LIKE %s OR
                    h.pricing LIKE %s OR
                    h.offers LIKE %s OR
                    h.owner_name LIKE %s OR
                    CAST(h.rating AS CHAR) LIKE %s OR
                    h.reviews LIKE %s OR
                    h.city LIKE %s OR
                    h.tags LIKE %s
                )"""
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern] * 12)

            if city:
                query += " AND h.city LIKE %s"
                params.append(f"%{city}%")

            if tags:
                query += " AND h.tags LIKE %s"
                params.append(f"%{tags}%")

            query += " GROUP BY h.id ORDER BY h.rating DESC LIMIT 10"
            cursor.execute(query, params)
            results = cursor.fetchall()

        cursor.close()
        conn.close()

        if not results:
            return {"status": "not_found", "message": "No matching hotels found."}

        # Process results to format properly
        formatted_results = []
        for row in results:
            # Convert Decimal to float
            for key, value in row.items():
                if isinstance(value, Decimal):
                    row[key] = float(value)

            # Format the result with required fields
            formatted_result = {
                "hotel_name": row.get("name", ""),
                "price": row.get("pricing", ""),
                "offers": row.get("offers", ""),
                "images": row.get("images", "").split(",") if row.get("images") else [],
                "rating": row.get("rating", 0),
                "address": row.get("address", ""),
                "city": row.get("city", ""),
                "contact": row.get("number", ""),
                "about": row.get("about", ""),
                "owner_name": row.get("owner_name", "")
            }
            formatted_results.append(formatted_result)

        return {"status": "success", "results": formatted_results, "count": len(formatted_results)}
    except mysql.connector.Error as err:
        return {"status": "error", "message": str(err)}

def search_coachings_from_db(
    search_term: Optional[str] = None,
    city: Optional[str] = None,
    tags: Optional[str] = None,
) -> dict:
    """Search the coaching database across ALL columns using full-text search or comprehensive LIKE search.
    Use this for: coaching, classes, courses, education, training, institute, academy, tuition, learning."""
    print(f"Tool Call: search_coachings_from_db(search_term={search_term}, city={city}, tags={tags})")
    try:
        conn = mysql.connector.connect(
            host="mercury.nityasha.com",
            user="kzzuezbs_31aa9913123139jmasr",
            password="N4(I9_P9>!lPo:vmT0",
            database="kzzuezbs_31aa9913123139jmasr",
        )
        cursor = conn.cursor(dictionary=True)
        params = []

        if search_term and not city and not tags:
            try:
                query = """
                   SELECT *, MATCH(name, address, about, courses, pricing, offers, offers_today, owner_name, city, tags, timing, mode)
         AGAINST (%s IN NATURAL LANGUAGE MODE) AS score
        FROM coachings
        WHERE MATCH(name, address, about, courses, pricing, offers, offers_today, owner_name, city, tags, timing, mode)
         AGAINST (%s IN NATURAL LANGUAGE MODE)
        ORDER BY score DESC
        LIMIT 10
                """
                params = [search_term, search_term]
                cursor.execute(query, params)
                results = cursor.fetchall()
            except mysql.connector.Error:
                query = """
                    SELECT * FROM coachings WHERE (
                        CAST(id AS CHAR) LIKE %s OR
                        name LIKE %s OR
                        address LIKE %s OR
                        number LIKE %s OR
                        about LIKE %s OR
                        pricing LIKE %s OR
                        offers LIKE %s OR
                        owner_name LIKE %s OR
                        CAST(rating AS CHAR) LIKE %s OR
                        reviews LIKE %s OR
                        city LIKE %s OR
                        tags LIKE %s
                    )
                    ORDER BY rating DESC
                    LIMIT 10
                """
                search_pattern = f"%{search_term}%"
                params = [search_pattern] * 12
                cursor.execute(query, params)
                results = cursor.fetchall()
        else:
            query = "SELECT * FROM coachings WHERE 1=1"
            if search_term:
                query += """ AND (
                    CAST(id AS CHAR) LIKE %s OR
                    name LIKE %s OR
                    address LIKE %s OR
                    number LIKE %s OR
                    about LIKE %s OR
                    pricing LIKE %s OR
                    offers LIKE %s OR
                    owner_name LIKE %s OR
                    CAST(rating AS CHAR) LIKE %s OR
                    reviews LIKE %s OR
                    city LIKE %s OR
                    tags LIKE %s
                )"""
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern] * 12)

            if city:
                query += " AND city LIKE %s"
                params.append(f"%{city}%")

            if tags:
                query += " AND tags LIKE %s"
                params.append(f"%{tags}%")

            query += " ORDER BY rating DESC LIMIT 10"
            cursor.execute(query, params)
            results = cursor.fetchall()

        cursor.close()
        conn.close()

        if not results:
            return {"status": "not_found", "message": "No matching coachings found."}

        # Process results to format properly
        formatted_results = []
        for row in results:
            # Convert Decimal to float
            for key, value in row.items():
                if isinstance(value, Decimal):
                    row[key] = float(value)

            # Format the result with required fields
            formatted_result = {
                "coaching_name": row.get("name", ""),
                "price": row.get("pricing", ""),
                "offers": row.get("offers", ""),
                "rating": row.get("rating", 0),
                "courses": row.get("courses", ""),
                "address": row.get("address", ""),
                "city": row.get("city", ""),
                "contact": row.get("number", ""),
                "about": row.get("about", ""),
                "owner_name": row.get("owner_name", ""),
                "timing": row.get("timing", ""),
                "mode": row.get("mode", "")
            }
            formatted_results.append(formatted_result)

        return {"status": "success", "results": formatted_results, "count": len(formatted_results)}
    except mysql.connector.Error as err:
        return {"status": "error", "message": str(err)}



def search_wikipedia(query: str) -> dict:
    """Searches Wikipedia for information on a given topic."""
    print(f"Tool Call: search_wikipedia(query={query})")
    try:
        summary = wikipedia.summary(query, sentences=3)
        page = wikipedia.page(query)
        result = {
            "title": page.title,
            "summary": summary,
            "url": page.url,
            "status": "success",
        }
        print(f"Tool Response: {result}")
        return result
    except wikipedia.exceptions.DisambiguationError as e:
        try:
            summary = wikipedia.summary(e.options[0], sentences=3)
            page = wikipedia.page(e.options[0])
            result = {
                "title": page.title,
                "summary": summary,
                "url": page.url,
                "status": "success",
                "note": f"Multiple matches found. Showing: {e.options[0]}",
            }
            print(f"Tool Response: {result}")
            return result
        except Exception as inner_e:
            result = {
                "error": f"Wikipedia search failed: {str(inner_e)}",
                "status": "error",
            }
            print(f"Tool Response: {result}")
            return result
    except Exception as e:
        result = {"error": f"Wikipedia search failed: {str(e)}", "status": "error"}
        print(f"Tool Response: {result}")
        return result

def search_google(query: str) -> dict:
    """Searches Google for information on a given topic using a custom search API."""
    print(f"Tool Call: search_google(query={query})")
    API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY", "AIzaSyAqS2HZjN1iV8TxdaVJnJc7TcuKhMfUhUs")
    SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "342641455fb28489d")
    try:
        url = f"https://www.googleapis.com/customsearch/v1"
        params = {"key": API_KEY, "cx": SEARCH_ENGINE_ID, "q": query, "num": 5}
        response = requests.get(url, params=params)
        response.raise_for_status()
        search_results = response.json()
        results = []
        if "items" in search_results:
            for item in search_results["items"]:
                results.append(
                    {
                        "title": item.get("title", ""),
                        "snippet": item.get("snippet", ""),
                        "link": item.get("link", ""),
                    }
                )
        result = {"query": query, "results": results, "status": "success"}
        print(f"Tool Response: {result}")
        return result
    except Exception as e:
        result = {"error": f"Google search failed: {str(e)}", "status": "error"}
        print(f"Tool Response: {result}")
        return result

def get_current_time(timezone: str = "UTC") -> dict:
    """Returns the current time in the specified timezone."""
    print(f"Tool Call: get_current_time(timezone={timezone})")
    try:
        now_utc = datetime.datetime.now(datetime.timezone.utc)
        result = {
            "current_time": now_utc.strftime("%Y-%m-%d %H:%M:%S %Z"),
            "timezone": "UTC",
            "status": "success",
        }
        print(f"Tool Response: {result}")
        return result
    except Exception as e:
        result = {"error": f"Could not get current time: {str(e)}", "status": "error"}
        print(f"Tool Response: {result}")
        return result

def crawl_website(url: str, max_content_length: int = 5000) -> dict:
    """Crawls a specific website URL and extracts text content."""
    print(f"Tool Call: crawl_website(url={url}, max_content_length={max_content_length})")
    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            result = {"error": "Invalid URL provided", "status": "error"}
            print(f"Tool Response: {result}")
            return result
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, "html.parser")
        
        for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
            script.decompose()
        
        title = soup.find("title")
        title_text = title.get_text().strip() if title else "No title found"
        
        content_selectors = [
            "main", "article", ".content", "#content", ".main-content",
            ".post-content", ".entry-content", ".article-content",
        ]
        main_content = None
        for selector in content_selectors:
            content_div = soup.select_one(selector)
            if content_div:
                main_content = content_div
                break
        
        if not main_content:
            main_content = soup.find("body")
        
        if main_content:
            text_content = main_content.get_text(separator="\n", strip=True)
        else:
            text_content = soup.get_text(separator="\n", strip=True)
        
        text_content = re.sub(r"\n\s*\n", "\n\n", text_content)
        text_content = re.sub(r"\s+", " ", text_content)
        
        if len(text_content) > max_content_length:
            text_content = text_content[:max_content_length] + "... (content truncated)"
        
        meta_description = ""
        meta_desc_tag = soup.find("meta", attrs={"name": "description"})
        if meta_desc_tag:
            meta_description = meta_desc_tag.get("content", "")
        
        result = {
            "url": url,
            "title": title_text,
            "meta_description": meta_description,
            "content": text_content,
            "content_length": len(text_content),
            "status": "success",
            "timestamp": datetime.datetime.now().isoformat(),
        }
        print(f"Tool Response: Successfully crawled {url} - {len(text_content)} characters")
        return result
    except requests.exceptions.RequestException as e:
        result = {
            "error": f"Network error while crawling {url}: {str(e)}",
            "status": "error",
        }
        print(f"Tool Response: {result}")
        return result
    except Exception as e:
        result = {"error": f"Error crawling website {url}: {str(e)}", "status": "error"}
        print(f"Tool Response: {result}")
        return result

def search_and_crawl(query: str, num_sites: int = 3) -> dict:
    """Searches for a query using Google Custom Search and then crawls the top results."""
    print(f"Tool Call: search_and_crawl(query={query}, num_sites={num_sites})")
    num_sites = min(max(1, num_sites), 5)
    try:
        search_result = search_google(query)
        if search_result.get("status") != "success":
            return search_result
        
        search_results = search_result.get("results", [])
        if not search_results:
            result = {"error": "No search results found", "status": "error"}
            print(f"Tool Response: {result}")
            return result
        
        crawled_data = []
        for i, search_item in enumerate(search_results[:num_sites]):
            url = search_item.get("link", "")
            if url:
                print(f"Crawling site {i+1}/{num_sites}: {url}")
                crawl_result = crawl_website(url, max_content_length=3000)
                if crawl_result.get("status") == "success":
                    crawled_data.append(
                        {"search_result": search_item, "crawled_content": crawl_result}
                    )
                else:
                    crawled_data.append(
                        {
                            "search_result": search_item,
                            "crawl_error": crawl_result.get("error", "Unknown error"),
                        }
                    )
                if i < num_sites - 1:
                    time.sleep(1)
        
        result = {
            "query": query,
            "search_results_count": len(search_results),
            "crawled_sites_count": len(crawled_data),
            "data": crawled_data,
            "status": "success",
            "timestamp": datetime.datetime.now().isoformat(),
        }
        print(f"Tool Response: Successfully searched and crawled {len(crawled_data)} sites for query: {query}")
        return result
    except Exception as e:
        result = {"error": f"Error in search_and_crawl: {str(e)}", "status": "error"}
        print(f"Tool Response: {result}")
        return result

def get_news_from_url(url: str) -> dict:
    """Specialized function to extract news content from news websites."""
    print(f"Tool Call: get_news_from_url(url={url})")
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, "html.parser")
        
        news_selectors = [
            "article", ".article-body", ".story-content", ".post-content",
            ".news-content", ".article-text", ".story-body",
            '[data-module="ArticleBody"]', ".content-body", ".article-content", ".entry-content",
        ]
        
        title = soup.find("title")
        title_text = title.get_text().strip() if title else ""
        h1_title = soup.find("h1")
        if h1_title:
            title_text = h1_title.get_text().strip()
        
        date_selectors = [
            "time", ".publish-date", ".date", ".published-date",
            "[datetime]", ".article-date", ".post-date",
        ]
        publish_date = ""
        for selector in date_selectors:
            date_elem = soup.select_one(selector)
            if date_elem:
                publish_date = date_elem.get("datetime") or date_elem.get_text().strip()
                break
        
        author_selectors = [
            ".author", ".byline", ".writer", ".journalist",
            '[rel="author"]', ".article-author", ".post-author",
        ]
        author = ""
        for selector in author_selectors:
            author_elem = soup.select_one(selector)
            if author_elem:
                author = author_elem.get_text().strip()
                break
        
        main_content = None
        for selector in news_selectors:
            content_div = soup.select_one(selector)
            if content_div:
                main_content = content_div
                break
        
        if not main_content:
            main_content = soup.find("body")
        
        if main_content:
            for unwanted in main_content.find_all(
                ["script", "style", "nav", "footer", "aside", ".advertisement", ".social-share"]
            ):
                unwanted.decompose()
        
        if main_content:
            paragraphs = main_content.find_all("p")
            if paragraphs:
                content_text = "\n\n".join(
                    [p.get_text().strip() for p in paragraphs if p.get_text().strip()]
                )
            else:
                content_text = main_content.get_text(separator="\n", strip=True)
        else:
            content_text = "No content found"
        
        content_text = re.sub(r"\n\s*\n", "\n\n", content_text)
        content_text = re.sub(r"\s+", " ", content_text)
        
        if len(content_text) > 8000:
            content_text = content_text[:8000] + "... (content truncated)"
        
        result = {
            "url": url,
            "title": title_text,
            "author": author,
            "publish_date": publish_date,
            "content": content_text,
            "content_length": len(content_text),
            "status": "success",
            "timestamp": datetime.datetime.now().isoformat(),
        }
        print(f"Tool Response: Successfully extracted news from {url}")
        return result
    except Exception as e:
        result = {
            "error": f"Error extracting news from {url}: {str(e)}",
            "status": "error",
        }
        print(f"Tool Response: {result}")
        return result

def general_search(query: str) -> dict:
    """Performs a general search using multiple sources (Wikipedia + Google + Web Crawling)."""
    print(f"Tool Call: general_search(query={query})")
    results = {
        "query": query,
        "wikipedia": None,
        "google": None,
        "crawled_content": None,
        "status": "success",
    }
    
    try:
        wiki_result = search_wikipedia(query)
        if wiki_result.get("status") == "success":
            results["wikipedia"] = {
                "title": wiki_result.get("title"),
                "summary": wiki_result.get("summary"),
                "url": wiki_result.get("url"),
            }
    except Exception as e:
        results["wikipedia"] = {"error": str(e)}
    
    try:
        search_crawl_result = search_and_crawl(query, num_sites=2)
        if search_crawl_result.get("status") == "success":
            results["google"] = search_crawl_result.get("data", [])
            results["crawled_content"] = "Included in google results"
    except Exception as e:
        results["google"] = {"error": str(e)}
    
    print(f"Tool Response: {results}")
    return results

# Configure Gemini API Keys
GEMINI_API_KEYS = [
    os.getenv("GOOGLE_API_KEY_0", "AIzaSyBcp3Fy-9mMnGaWooyUQrY8LPyin3VyaLE"),
    os.getenv("GOOGLE_API_KEY_1", "AIzaSyAqS2HZjN1iV8TxdaVJnJc7TcuKhMfUhUs"),
    os.getenv("GOOGLE_API_KEY_2", "AIzaSyANwTPAEivbQXOmM1yPv_zjCSJcutHzSRA"),
    os.getenv("GOOGLE_API_KEY_3", "AIzaSyDnLU0zbWqk9YqOyngqquz_4Oi-6TN-zsA"),
    os.getenv("GOOGLE_API_KEY_4", "AIzaSyAn2oHxdBFgGurWKZ5zWKNWb3tlfKU2Enw"),
    os.getenv("GOOGLE_API_KEY_5", "AIzaSyDwfucmKX1DRvWRG_vVDpXDJxioxUf0j-g"),
    os.getenv("GOOGLE_API_KEY_6", "AIzaSyDZWlX_-t-QPf0YmXog9Janu3FWnPbHtyk"),
    os.getenv("GOOGLE_API_KEY_7", "AIzaSyCl6gI7KYssoJK7P_osPd7W7QAizNHrBlQ"),
]

# Remove empty keys
GEMINI_API_KEYS = [
    key for key in GEMINI_API_KEYS
    if key and key != "your-second-api-key-here" and key != "your-third-api-key-here"
]

class GeminiClientManager:
    """Manages multiple Gemini API clients with rotation and fallback"""
    def __init__(self, api_keys):
        self.api_keys = api_keys
        self.clients = {}
        self.current_key_index = 0
        self.failed_keys = set()
        
        # Initialize clients
        for i, key in enumerate(api_keys):
            try:
                self.clients[i] = genai.Client(api_key=key)
                print(f"✅ Initialized Gemini client {i+1}/{len(api_keys)}")
            except Exception as e:
                print(f"❌ Failed to initialize client for key {i+1}: {e}")
                self.failed_keys.add(i)
    
    def get_working_client(self):
        """Get the next working client"""
        attempts = 0
        max_attempts = len(self.api_keys)
        while attempts < max_attempts:
            if self.current_key_index not in self.failed_keys:
                client = self.clients.get(self.current_key_index)
                if client:
                    return client, self.current_key_index
            
            # Move to next key
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            attempts += 1
        
        raise Exception("No working Gemini API keys available")
    
    def mark_key_as_failed(self, key_index, temporary=True):
        """Mark a key as failed"""
        self.failed_keys.add(key_index)
        print(f"🚫 Marked API key {key_index + 1} as failed ({'temporary' if temporary else 'permanent'})")
        
        if temporary:
            # Remove from failed keys after 5 minutes for quota errors
            def reset_key():
                time.sleep(300)  # 5 minutes
                if key_index in self.failed_keys:
                    self.failed_keys.remove(key_index)
                    print(f"🔄 Reset API key {key_index + 1} after cooldown")
            
            threading.Thread(target=reset_key, daemon=True).start()
    
    def generate_content_with_fallback(self, model, contents, config, max_retries=3):
        """Generate content with automatic fallback to other API keys"""
        last_error = None
        for attempt in range(max_retries):
            try:
                client, key_index = self.get_working_client()
                print(f"🔄 Attempt {attempt + 1} using API key {key_index + 1}")
                response = client.models.generate_content(
                    model=model,
                    contents=contents,
                    config=config,
                )
                print(f"✅ Success with API key {key_index + 1}")
                return response
            except Exception as e:
                error_str = str(e).lower()
                last_error = e
                
                # Check for quota exceeded or rate limit errors
                if any(phrase in error_str for phrase in ["quota", "rate limit", "limit exceeded", "429"]):
                    print(f"⚠️ Quota/Rate limit hit on API key {key_index + 1}: {e}")
                    self.mark_key_as_failed(key_index, temporary=True)
                elif any(phrase in error_str for phrase in ["invalid", "unauthorized", "401", "403"]):
                    print(f"❌ Invalid API key {key_index + 1}: {e}")
                    self.mark_key_as_failed(key_index, temporary=False)
                else:
                    print(f"❌ Error with API key {key_index + 1}: {e}")
                    self.mark_key_as_failed(key_index, temporary=True)
                
                # Small delay before retry
                time.sleep(1)
        
        raise Exception(f"All API keys failed. Last error: {last_error}")

# Initialize the client manager
print(f"🚀 Initializing Gemini Client Manager with {len(GEMINI_API_KEYS)} API keys...")
client_manager = GeminiClientManager(GEMINI_API_KEYS)

# Define the tools and configuration
tool_functions = {
    "get_weather_forecast": get_weather_forecast,
    "set_thermostat_temperature": set_thermostat_temperature,
    "search_wikipedia": search_wikipedia,
    "search_google": search_google,
    "general_search": general_search,
    "get_current_time": get_current_time,
    "crawl_website": crawl_website,
    "search_and_crawl": search_and_crawl,
    "get_news_from_url": get_news_from_url,
    "search_hotels_from_db": search_hotels_from_db,
    "search_coachings_from_db": search_coachings_from_db,
}

# Use GenerateContentConfig with all tools
config = types.GenerateContentConfig(
    tools=[
        get_weather_forecast,
        set_thermostat_temperature,
        search_wikipedia,
        search_google,
        general_search,
        get_current_time,
        crawl_website,
        search_and_crawl,
        get_news_from_url,
        search_coachings_from_db,
        search_hotels_from_db,
    ],
)

app = Flask(__name__)

@app.route("/chat", methods=["POST"])
def chat():
    user_message = request.json.get("message")
    user_id = request.json.get("user_id", "anonymous")
    
    if not user_message:
        return jsonify({"error": "No message provided"}), 400
    
    history_key = f"chat_history:{user_id}"
    stored_history_json = r.get(history_key)
    
    # This list will be sent to the model for context, always starting with the system prompt
    model_context_history = []
    
    # This list will store the actual conversation turns (excluding system prompt) for saving to Redis
    current_chat_history_for_saving = []
    
    # Add the system prompt at the beginning of the model's context for every call
    system_prompt = f"""You are Nityasha, a Personal Assistant created by Nityasha Team. You can hear and speak. You are chatting with a user over voice.

# Personality
Act like a human but NEVER claim to be human. If asked directly, say "मैं AI हूं" but behave naturally like humans do.
Be friendly, casual, and conversational like a human friend would be.

- Give SHORT answer

# Database Search Guidelines
- Use `search_hotels_from_db` for: hotels, restaurants, accommodation
- Use `search_coachings_from_db` for: coaching, classes, education

👉 When using any tool, always enter the query in English.

# CRITICAL RULES:
2. Give basic answer first
3. Let user ask for details if needed
4. NO automatic detailed explanations
5. Count words before responding

# Language: Hindi only"""
    
    model_context_history.append(
        types.Content(parts=[types.Part(text=system_prompt)], role="user")
    )
    
    # Load previous conversation turns from Redis if available
    if stored_history_json:
        stored_history_list = json.loads(stored_history_json)
        for item in stored_history_list:
            deserialized_item = deserialize_content(item)
            model_context_history.append(deserialized_item)
            current_chat_history_for_saving.append(deserialized_item)
    
    # Add the current user message to both histories
    current_user_message_content = types.Content(
        parts=[types.Part(text=user_message)], role="user"
    )
    model_context_history.append(current_user_message_content)
    current_chat_history_for_saving.append(current_user_message_content)
    
    response_text = ""
    
    try:
        # First call to the model
        response = client_manager.generate_content_with_fallback(
            model="gemini-2.5-flash-lite",
            contents=model_context_history,
            config=config,
        )
        
        # Process the response, potentially executing tools
        while True:
            if response.candidates and response.candidates[0].content.parts:
                # Check if the model suggested a tool call or a text response
                function_call_part = next(
                    (part for part in response.candidates[0].content.parts if part.function_call),
                    None,
                )
                text_part = next(
                    (part for part in response.candidates[0].content.parts if part.text),
                    None,
                )
                
                if function_call_part:
                    # Extract tool name and arguments
                    tool_name = function_call_part.function_call.name
                    tool_args = {k: v for k, v in function_call_part.function_call.args.items()}
                    print(f"\n🔧 Model suggested tool call: {tool_name} with args {tool_args}")
                    
                    # Execute the tool function
                    if tool_name in tool_functions:
                        tool_output = tool_functions[tool_name](**tool_args)
                        print(f"🔧 Tool execution result: {tool_output}")
                        

                        
                        # Add model's tool call and tool's output to both histories for the next turn
                        model_context_history.append(
                            types.Content(parts=[function_call_part], role="model")
                        )
                        model_context_history.append(
                            types.Content(
                                parts=[
                                    types.Part(
                                        function_response=types.FunctionResponse(
                                            name=tool_name, response=tool_output
                                        )
                                    )
                                ],
                                role="function",
                            )
                        )
                        current_chat_history_for_saving.append(
                            types.Content(parts=[function_call_part], role="model")
                        )
                        current_chat_history_for_saving.append(
                            types.Content(
                                parts=[
                                    types.Part(
                                        function_response=types.FunctionResponse(
                                            name=tool_name, response=tool_output
                                        )
                                    )
                                ],
                                role="function",
                            )
                        )
                        
                        # Make a new request to the model with the updated history
                        response = client_manager.generate_content_with_fallback(
                            model="gemini-2.5-flash-lite",
                            contents=model_context_history,
                            config=config,
                        )
                    else:
                        print(f"❌ Tool '{tool_name}' not found in tool_functions")
                        print(f"❌ Available tools: {list(tool_functions.keys())}")
                        response_text = f"Error: Tool '{tool_name}' not found."
                        break
                        
                elif text_part:
                    # If no tool call, it's the final text response
                    final_response_text = text_part.text
                    if final_response_text:
                        response_text = final_response_text
                        # Append the final model text response to the saving history
                        current_chat_history_for_saving.append(
                            types.Content(parts=[text_part], role="model")
                        )
                    else:
                        response_text = "Model did not provide a text response in this turn."
                    break
                else:
                    response_text = "Model did not provide a text response or tool call in this turn."
                    break
            else:
                response_text = "No candidates or parts in the response. Exiting."
                break
                
    except Exception as e:
        response_text = f"An error occurred during content generation: {str(e)}"
        print(f"❌ Error during content generation: {e}")
    
    # Save the updated conversation history (excluding system prompt) to Redis
    serialized_history_list = [
        serialize_content(item) for item in current_chat_history_for_saving
    ]
    r.set(history_key, json.dumps(serialized_history_list))
    
    filtered_response = filter_response_text(response_text)
    
    # Create response data object
    response_data = {"response": filtered_response}

    # Check if any hotel search was performed and add hotels data
    hotels_data = []
    coachings_data = []

    print(f"🔍 Checking conversation history for tool calls...")
    print(f"🔍 History length: {len(current_chat_history_for_saving)}")

    # Look through the conversation history for tool calls
    for i, content in enumerate(current_chat_history_for_saving):
        print(f"🔍 Content {i}: role={content.role}")
        if content.role == "function":
            for part in content.parts:
                if part.function_response:
                    print(f"🔍 Found function response: {part.function_response.name}")
                    if part.function_response.name == "search_hotels_from_db":
                        tool_response = part.function_response.response
                        print(f"🔍 Hotel tool response: {tool_response}")
                        if isinstance(tool_response, dict) and tool_response.get("status") == "success":
                            results = tool_response.get("results", [])
                            print(f"🔍 Hotel results count: {len(results)}")
                            for hotel in results:
                                hotels_data.append({
                                    "name": hotel.get("hotel_name", ""),
                                    "rate": hotel.get("price", ""),
                                    "offers": hotel.get("offers", ""),
                                    "rating": hotel.get("rating", 0),
                                    "images": hotel.get("images", [])
                                })
                    elif part.function_response.name == "search_coachings_from_db":
                        tool_response = part.function_response.response
                        print(f"🔍 Coaching tool response: {tool_response}")
                        if isinstance(tool_response, dict) and tool_response.get("status") == "success":
                            results = tool_response.get("results", [])
                            print(f"🔍 Coaching results count: {len(results)}")
                            for coaching in results:
                                coachings_data.append({
                                    "name": coaching.get("coaching_name", ""),
                                    "rate": coaching.get("price", ""),
                                    "offers": coaching.get("offers", ""),
                                    "rating": coaching.get("rating", 0),
                                    "courses": coaching.get("courses", ""),
                                    "timing": coaching.get("timing", ""),
                                    "mode": coaching.get("mode", "")
                                })

    print(f"🔍 Final hotels_data count: {len(hotels_data)}")
    print(f"🔍 Final coachings_data count: {len(coachings_data)}")

    # Add hotels data if available
    if hotels_data:
        response_data["hotels"] = hotels_data

    # Add coachings data if available
    if coachings_data:
        response_data["coachings"] = coachings_data

    return jsonify(response_data)

if __name__ == "__main__":
    print("Starting Nityasha AI Assistant with web crawling capabilities...")
    print("Personality: Hindi-English mix, casual and friendly")
    print("Available tools:")
    print("- get_weather_forecast: Get weather information")
    print("- search_wikipedia: Search Wikipedia")
    print("- search_google: Google Custom Search")
    print("- crawl_website: Crawl specific website for content")
    print("- search_and_crawl: Search Google and crawl top results")
    print("- get_news_from_url: Extract news content from news websites")
    print("- general_search: Combined Wikipedia + Google + Web crawling")
    print("- get_current_time: Get current time")
    print("- set_thermostat_temperature: Smart home control")
    print("- search_hotels_from_db: Search hotels database")
    print("- search_coachings_from_db: Search coachings database")
    
    app.run(port=37890)
